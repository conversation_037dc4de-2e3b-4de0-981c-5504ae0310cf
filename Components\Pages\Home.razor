﻿@page "/"
@using Microsoft.FluentUI.AspNetCore.Components

<PageTitle>BlazorOCR - Home</PageTitle>

<FluentStack Orientation="Orientation.Vertical" Style="padding: 20px; max-width: 1000px; margin: 0 auto;">

    <FluentCard Style="padding: 32px; margin-bottom: 24px; text-align: center;">
        <FluentStack Orientation="Orientation.Vertical">
            <FluentLabel Typo="Typography.H2" Style="margin-bottom: 16px;">
                📄 BlazorOCR
            </FluentLabel>
            <FluentLabel Typo="Typography.H5" Style="color: var(--neutral-foreground-rest); margin-bottom: 24px;">
                Advanced TIFF OCR Text Extraction
            </FluentLabel>
            <FluentLabel Typo="Typography.Body" Style="max-width: 600px; margin: 0 auto;">
                Extract text from multi-page TIFF files with support for English and Japanese languages using advanced OCR technology.
            </FluentLabel>
        </FluentStack>
    </FluentCard>

    <FluentGrid>
        <FluentGridItem xs="12" md="6">
            <FluentCard Style="padding: 24px; height: 100%;">
                <FluentStack Orientation="Orientation.Vertical">
                    <FluentLabel Typo="Typography.H6" Style="margin-bottom: 12px;">
                        🚀 Features
                    </FluentLabel>
                    <FluentStack Orientation="Orientation.Vertical" Style="gap: 8px;">
                        <FluentLabel>✅ Multi-page TIFF support</FluentLabel>
                        <FluentLabel>🌐 English & Japanese text recognition</FluentLabel>
                        <FluentLabel>📊 Organized text extraction</FluentLabel>
                        <FluentLabel>💻 Interactive web interface</FluentLabel>
                        <FluentLabel>🔒 Secure file processing</FluentLabel>
                    </FluentStack>
                </FluentStack>
            </FluentCard>
        </FluentGridItem>

        <FluentGridItem xs="12" md="6">
            <FluentCard Style="padding: 24px; height: 100%;">
                <FluentStack Orientation="Orientation.Vertical">
                    <FluentLabel Typo="Typography.H6" Style="margin-bottom: 12px;">
                        📋 How to Use
                    </FluentLabel>
                    <FluentStack Orientation="Orientation.Vertical" Style="gap: 8px;">
                        <FluentLabel>1. Navigate to TIFF OCR page</FluentLabel>
                        <FluentLabel>2. Select your TIFF file (max 50MB)</FluentLabel>
                        <FluentLabel>3. Wait for processing to complete</FluentLabel>
                        <FluentLabel>4. View extracted text by language</FluentLabel>
                        <FluentLabel>5. Process additional files as needed</FluentLabel>
                    </FluentStack>
                </FluentStack>
            </FluentCard>
        </FluentGridItem>
    </FluentGrid>

    <FluentCard Style="padding: 24px; margin-top: 24px; text-align: center;">
        <FluentStack Orientation="Orientation.Vertical">
            <FluentLabel Typo="Typography.H6" Style="margin-bottom: 16px;">
                Ready to get started?
            </FluentLabel>
            <FluentButton Appearance="Appearance.Accent"
                          Style="width: fit-content; margin: 0 auto;"
                          OnClick="@(() => Navigation.NavigateTo("/tiff-ocr"))"
                          IconStart="@(new Icons.Regular.Size20.DocumentText())">
                Start OCR Processing
            </FluentButton>
        </FluentStack>
    </FluentCard>

</FluentStack>

@code {
    [Inject] private NavigationManager Navigation { get; set; } = default!;
}