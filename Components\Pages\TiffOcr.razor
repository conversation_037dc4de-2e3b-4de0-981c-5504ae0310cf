@page "/tiff-ocr"
@using BlazorOCR.Services
@using Microsoft.FluentUI.AspNetCore.Components
@inject OcrService OcrService
@inject ILogger<TiffOcr> Logger
@rendermode InteractiveServer

<PageTitle>TIFF OCR Processor</PageTitle>

<FluentStack Orientation="Orientation.Vertical" Style="padding: 20px; max-width: 1200px; margin: 0 auto;">
    
    <FluentCard Style="padding: 24px; margin-bottom: 20px;">
        <FluentStack Orientation="Orientation.Vertical">
            <FluentLabel Typo="Typography.H3" Style="margin-bottom: 16px;">
                📄 TIFF OCR Text Extractor
            </FluentLabel>
            <FluentLabel Typo="Typography.Body" Style="color: var(--neutral-foreground-rest); margin-bottom: 20px;">
                Upload a multi-page TIFF file to extract text using OCR technology. Supports English and Japanese languages.
            </FluentLabel>
        </FluentStack>
    </FluentCard>

    @if (!isProcessing && ocrResult == null)
    {
        <FluentCard Style="padding: 24px;">
            <FluentStack Orientation="Orientation.Vertical">
                <FluentLabel Typo="Typography.H5" Style="margin-bottom: 16px;">
                    📁 Select TIFF File
                </FluentLabel>
                
                <InputFile OnChange="@OnFileSelected"
                           accept=".tiff,.tif"
                           style="margin-bottom: 16px; padding: 8px; border: 1px solid var(--neutral-stroke-rest); border-radius: 4px; width: 100%;" />
                
                <FluentLabel Typo="Typography.Body" Style="color: var(--neutral-foreground-rest); font-size: 0.875rem;">
                    Only TIFF files are supported. Maximum file size: 50MB
                </FluentLabel>
                
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <FluentMessageBar Intent="MessageIntent.Error" Style="margin-top: 16px;">
                        @errorMessage
                    </FluentMessageBar>
                }
            </FluentStack>
        </FluentCard>
    }

    @if (isProcessing)
    {
        <FluentCard Style="padding: 24px;">
            <FluentStack Orientation="Orientation.Vertical" HorizontalAlignment="HorizontalAlignment.Center">
                <FluentProgressRing Style="margin-bottom: 16px;" />
                <FluentLabel Typo="Typography.H6">Processing TIFF file...</FluentLabel>
                <FluentLabel Typo="Typography.Body" Style="color: var(--neutral-foreground-rest);">
                    Extracting text from @selectedFileName
                </FluentLabel>
            </FluentStack>
        </FluentCard>
    }

    @if (ocrResult != null)
    {
        <FluentCard Style="padding: 24px;">
            <FluentStack Orientation="Orientation.Vertical">
                <FluentStack Orientation="Orientation.Horizontal" HorizontalAlignment="HorizontalAlignment.SpaceBetween" Style="margin-bottom: 20px;">
                    <FluentLabel Typo="Typography.H5">
                        📊 OCR Results
                    </FluentLabel>
                    <FluentButton Appearance="Appearance.Accent"
                                  OnClick="ResetForm"
                                  IconStart="@(new Icons.Regular.Size16.ArrowClockwise())">
                        Process Another File
                    </FluentButton>
                </FluentStack>

                @if (ocrResult.IsSuccess)
                {
                    <FluentStack Orientation="Orientation.Horizontal" Style="margin-bottom: 16px;">
                        <FluentBadge Fill="someValue" BackgroundColor="var(--accent-fill-rest)" Color="white">
                            📄 @ocrResult.TotalPages pages
                        </FluentBadge>
                        <FluentBadge Fill="someValue" BackgroundColor="var(--success-fill-rest)" Color="white">
                            ✅ Processed successfully
                        </FluentBadge>
                    </FluentStack>

                    @if (ocrResult.Pages.Any())
                    {
                        var firstPage = ocrResult.Pages.First();
                        
                        <FluentLabel Typo="Typography.H6" Style="margin: 20px 0 12px 0;">
                            📝 Extracted Text from Page 1
                        </FluentLabel>

                        <FluentTabs>
                            @foreach (var textEntry in firstPage.ExtractedText)
                            {
                                <FluentTab Text="@GetTabLabel(textEntry.Key)">
                                    <FluentCard Style="margin-top: 16px; padding: 16px; background-color: var(--neutral-layer-2);">
                                        @if (!string.IsNullOrWhiteSpace(textEntry.Value))
                                        {
                                            <pre style="white-space: pre-wrap; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.5; margin: 0;">@textEntry.Value</pre>
                                        }
                                        else
                                        {
                                            <FluentLabel Style="color: var(--neutral-foreground-rest); font-style: italic;">
                                                No text detected for @textEntry.Key language
                                            </FluentLabel>
                                        }
                                    </FluentCard>
                                </FluentTab>
                            }
                        </FluentTabs>
                    }
                }
                else
                {
                    <FluentMessageBar Intent="MessageIntent.Error">
                        <strong>Processing Failed:</strong> @ocrResult.ErrorMessage
                    </FluentMessageBar>
                }
            </FluentStack>
        </FluentCard>
    }

</FluentStack>

@code {
    private bool isProcessing = false;
    private string selectedFileName = string.Empty;
    private string errorMessage = string.Empty;
    private OcrResult? ocrResult;

    private async Task OnFileSelected(InputFileChangeEventArgs e)
    {
        errorMessage = string.Empty;

        var file = e.File;
        if (file == null) return;

        // Validate file type
        if (!IsValidTiffFile(file.Name))
        {
            errorMessage = "Please select a valid TIFF file (.tiff or .tif extension).";
            return;
        }

        // Validate file size (50MB limit)
        if (file.Size > 50 * 1024 * 1024)
        {
            errorMessage = "File size exceeds 50MB limit. Please select a smaller file.";
            return;
        }

        selectedFileName = file.Name;
        isProcessing = true;
        StateHasChanged();

        try
        {
            using var stream = file.OpenReadStream(maxAllowedSize: 50 * 1024 * 1024);
            ocrResult = await OcrService.ProcessTiffFileAsync(stream, file.Name);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing file: {FileName}", file.Name);
            errorMessage = $"Error processing file: {ex.Message}";
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
        }
    }

    private bool IsValidTiffFile(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension == ".tiff" || extension == ".tif";
    }

    private void ResetForm()
    {
        ocrResult = null;
        selectedFileName = string.Empty;
        errorMessage = string.Empty;
        StateHasChanged();
    }

    private string GetTabLabel(string language)
    {
        return language switch
        {
            "English" => "🇺🇸 English",
            "Japanese" => "🇯🇵 Japanese",
            "Combined" => "🌐 Combined",
            _ => language
        };
    }
}
