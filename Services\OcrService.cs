using System.Drawing;
using System.Drawing.Imaging;
using System.Text;
using Tesseract;

namespace BlazorOCR.Services;

public class OcrService
{
    private readonly ILogger<OcrService> _logger;
    private readonly string _tessDataPath;

    public OcrService(ILogger<OcrService> logger)
    {
        _logger = logger;
        _tessDataPath = Path.Combine(Directory.GetCurrentDirectory(), "tessdata");
        
        // Ensure tessdata directory exists
        if (!Directory.Exists(_tessDataPath))
        {
            Directory.CreateDirectory(_tessDataPath);
        }
    }

    public async Task<OcrResult> ProcessTiffFileAsync(Stream fileStream, string fileName)
    {
        try
        {
            _logger.LogInformation("Starting OCR processing for file: {FileName}", fileName);
            
            var result = new OcrResult
            {
                FileName = fileName,
                ProcessedAt = DateTime.UtcNow,
                Pages = new List<OcrPageResult>()
            };

            // Load TIFF image
            using var image = Image.FromStream(fileStream);
            
            // Get number of pages in TIFF
            int pageCount = image.GetFrameCount(FrameDimension.Page);
            result.TotalPages = pageCount;
            
            _logger.LogInformation("TIFF file has {PageCount} pages", pageCount);

            // Process first page only as requested
            if (pageCount > 0)
            {
                image.SelectActiveFrame(FrameDimension.Page, 0);
                var pageResult = await ProcessPageAsync(image, 1);
                result.Pages.Add(pageResult);
            }

            result.IsSuccess = true;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing TIFF file: {FileName}", fileName);
            return new OcrResult
            {
                FileName = fileName,
                ProcessedAt = DateTime.UtcNow,
                IsSuccess = false,
                ErrorMessage = ex.Message
            };
        }
    }

    private async Task<OcrPageResult> ProcessPageAsync(Image pageImage, int pageNumber)
    {
        var pageResult = new OcrPageResult
        {
            PageNumber = pageNumber,
            ExtractedText = new Dictionary<string, string>()
        };

        try
        {
            // Convert to bitmap for Tesseract processing
            using var bitmap = new Bitmap(pageImage);
            
            // Process with English
            var englishText = await ExtractTextAsync(bitmap, "eng");
            pageResult.ExtractedText["English"] = englishText;
            
            // Process with Japanese
            var japaneseText = await ExtractTextAsync(bitmap, "jpn");
            pageResult.ExtractedText["Japanese"] = japaneseText;
            
            // Combined text
            var combinedText = await ExtractTextAsync(bitmap, "eng+jpn");
            pageResult.ExtractedText["Combined"] = combinedText;
            
            pageResult.IsSuccess = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing page {PageNumber}", pageNumber);
            pageResult.IsSuccess = false;
            pageResult.ErrorMessage = ex.Message;
        }

        return pageResult;
    }

    private async Task<string> ExtractTextAsync(Bitmap bitmap, string language)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var engine = new TesseractEngine(_tessDataPath, language, EngineMode.Default);
                using var pix = Pix.LoadFromMemory(BitmapToByteArray(bitmap));
                using var page = engine.Process(pix);
                
                var text = page.GetText();
                var confidence = page.GetMeanConfidence();
                
                _logger.LogInformation("OCR completed for language {Language} with confidence {Confidence:F2}", 
                    language, confidence);
                
                return text?.Trim() ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract text for language {Language}", language);
                return $"Error extracting text for {language}: {ex.Message}";
            }
        });
    }

    private byte[] BitmapToByteArray(Bitmap bitmap)
    {
        using var stream = new MemoryStream();
        bitmap.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
        return stream.ToArray();
    }

    public async Task<bool> DownloadTessDataAsync()
    {
        try
        {
            // This is a simplified version - in production, you'd want to download
            // the actual tessdata files from GitHub or include them in your deployment
            _logger.LogInformation("Checking tessdata files...");

            var requiredFiles = new[] { "eng.traineddata", "jpn.traineddata" };
            var allFilesExist = requiredFiles.All(file =>
                File.Exists(Path.Combine(_tessDataPath, file)));

            if (!allFilesExist)
            {
                _logger.LogWarning("Some tessdata files are missing. Please ensure eng.traineddata and jpn.traineddata are in the tessdata folder.");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tessdata files");
            return false;
        }
    }
}

public class OcrResult
{
    public string FileName { get; set; } = string.Empty;
    public DateTime ProcessedAt { get; set; }
    public int TotalPages { get; set; }
    public List<OcrPageResult> Pages { get; set; } = new();
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
}

public class OcrPageResult
{
    public int PageNumber { get; set; }
    public Dictionary<string, string> ExtractedText { get; set; } = new();
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
}
