# Tesseract Language Data Files

This directory should contain the Tesseract language data files required for OCR processing.

## Required Files

For this application to work properly, you need to download the following files:

1. **eng.traineddata** - English language data
2. **jpn.traineddata** - Japanese language data

## Download Instructions

1. Go to the official Tesseract tessdata repository:
   https://github.com/tesseract-ocr/tessdata

2. Download the following files and place them in this `tessdata` directory:
   - [eng.traineddata](https://github.com/tesseract-ocr/tessdata/raw/main/eng.traineddata)
   - [jpn.traineddata](https://github.com/tesseract-ocr/tessdata/raw/main/jpn.traineddata)

## Alternative: Fast Models

For faster processing (but potentially lower accuracy), you can use the fast models from:
https://github.com/tesseract-ocr/tessdata_fast

## File Structure

After downloading, your tessdata directory should look like:
```
tessdata/
├── README.md (this file)
├── eng.traineddata
└── jpn.traineddata
```

## Note

The application will check for these files at startup and log warnings if they are missing.
